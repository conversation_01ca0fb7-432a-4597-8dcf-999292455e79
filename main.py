#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
双色球机选（Gates & Buffett 主题加权版）
说明：
- 仍然是随机生成，仅做“主题化权重”处理，毫无保证提升中奖概率的含义。
- 红球：1–33 选 6 个不重复，蓝球：1–16 选 1 个。
- 同一轮内不出现“整组完全相同”的重复。
"""

import random
from datetime import datetime

N_GROUPS = 5
rng = random.SystemRandom()  # 使用系统级随机源

# ===== 将事实映射为号码权重 =====
FACTS = {
    # Bill Gates
    "gates_birth": (1955, 10, 28),   # 年, 月, 日
    "ms_founded": 1975,
    "ms_ipo": 1986,
    "gates_foundation": 2000,
    "giving_pledge": 2010,

    # <PERSON> Buffett
    "buffett_birth": (1930, 8, 30),
    "brk_control": 1965,  # 1965 年开始掌控伯克希尔
}

NAME_LENGTHS = {
    "Bill Gates": len("BillGates"),          # 9
    "<PERSON> Buffett": len("WarrenBuffett"),  # 13
}

def map_to_range(n: int, upper: int) -> int:
    """将任意整数 n 映射到 [1, upper] 闭区间（按取模，0 归到上限）。"""
    m = n % upper
    return upper if m == 0 else m

def build_weight_vector(upper: int, base: float = 1.0) -> list:
    """初始化权重向量（下标1..upper有效，0位占位）。"""
    return [0.0] + [base] * upper

def boost(weights: list, numbers, amount: float):
    """对若干号码加权。numbers 可迭代；越界会被忽略。"""
    for x in numbers:
        if 1 <= x < len(weights):
            weights[x] += amount

def themed_weights():
    """构建红/蓝球权重（结合 Gates & Buffett 主题数字）。"""
    red_w = build_weight_vector(33, base=1.0)
    blue_w = build_weight_vector(16, base=1.0)

    # 1) 生日的 月、日 直接是强偏好（红/蓝都可用）
    for (_, m, d) in (FACTS["gates_birth"], FACTS["buffett_birth"]):
        boost(red_w, [m, d], amount=2.2)
        boost(blue_w, [map_to_range(m, 16), map_to_range(d, 16)], amount=2.2)

    # 2) 关键年份（前两位 & 后两位），映射到区间
    years = [
        FACTS["gates_birth"][0], FACTS["ms_founded"], FACTS["ms_ipo"],
        FACTS["gates_foundation"], FACTS["giving_pledge"],
        FACTS["buffett_birth"][0], FACTS["brk_control"]
    ]
    for y in years:
        hi = y // 100           # 如 19
        lo = y % 100            # 如 55
        # 红球：直接用 hi；lo 映射到 1..33
        boost(red_w, [hi], amount=1.4)
        boost(red_w, [map_to_range(lo, 33)], amount=1.6)
        # 蓝球：hi/lo 都映射到 1..16
        boost(blue_w, [map_to_range(hi, 16), map_to_range(lo, 16)], amount=1.2)

    # 3) 名字长度（趣味偏好）
    for _name, length in NAME_LENGTHS.items():
        boost(red_w, [map_to_range(length, 33)], amount=1.0)
        boost(blue_w, [map_to_range(length, 16)], amount=0.8)

    # 4) 额外：Buffett 掌管伯克希尔年份 1965 => 65→红32、蓝1（更强一点）
    boost(red_w, [map_to_range(65, 33)], amount=1.2)
    boost(blue_w, [map_to_range(65, 16)], amount=1.0)

    return red_w, blue_w

def weighted_sample_without_replacement(population, weights, k):
    """
    按权重无放回抽样（Efraimidis-Spirakis 算法）。
    population: [1..N]
    weights: 与 population 对应的权重（下标即号码）
    返回：长度 k 的升序列表
    """
    # 给每个元素生成一个 key = U^(1/w)，取 key 最大的前 k 个
    keys = []
    for x in population:
        w = max(1e-9, float(weights[x]))
        u = rng.random()
        key = u ** (1.0 / w)
        keys.append((key, x))
    # 取最大的 k 个
    topk = sorted(keys, key=lambda t: t[0], reverse=True)[:k]
    return sorted(x for _, x in topk)

def generate_group(red_weights, blue_weights):
    """生成一组号码：(tuple(sorted 6 red), blue)"""
    reds = weighted_sample_without_replacement(list(range(1, 34)), red_weights, 6)
    blue = weighted_sample_without_replacement(list(range(1, 17)), blue_weights, 1)[0]
    return tuple(reds), blue

def main():
    red_w, blue_w = themed_weights()
    print(f"双色球机选（Gates & Buffett 主题加权）{N_GROUPS} 组  ——  {datetime.now():%Y-%m-%d %H:%M:%S}")
    print("提示：仅作趣味主题，随机本质不变，不构成任何概率建议。\n")

    seen = set()
    count = 0
    while count < N_GROUPS:
        reds, blue = generate_group(red_w, blue_w)
        key = reds + (blue,)
        if key in seen:
            continue
        seen.add(key)
        count += 1
        print(f"第{count}组：红球 {' '.join(f'{n:02d}' for n in reds)}  |  蓝球 {blue:02d}")

    # 打印本轮涉及的一些“主题数字”（便于理解来源）
    theme_preview_red = []
    theme_preview_blue = []

    # 从事实简单罗列（不穷举全部加权来源，只展示直观的几个）
    m1, d1 = FACTS["gates_birth"][1], FACTS["gates_birth"][2]
    m2, d2 = FACTS["buffett_birth"][1], FACTS["buffett_birth"][2]
    theme_preview_red += [m1, d1, m2, d2, 19, map_to_range(55, 33), map_to_range(75, 33),
                          map_to_range(86, 33), map_to_range(65, 33)]
    theme_preview_blue += [map_to_range(x, 16) for x in [m1, d1, m2, d2, 19, 55, 75, 86, 65]]

    theme_preview_red = sorted(set(theme_preview_red))
    theme_preview_blue = sorted(set(theme_preview_blue))

    print("\n（主题来源举例）可能被提高权重的红球：", " ".join(f"{x:02d}" for x in theme_preview_red))
    print("（主题来源举例）可能被提高权重的蓝球：", " ".join(f"{x:02d}" for x in theme_preview_blue))

if __name__ == "__main__":
    main()
